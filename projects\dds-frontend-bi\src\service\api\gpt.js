
// export function fetchChatAPIProcess(params) {
//   return Axios.post(
//     "http://127.0.0.1:4523/m1/555446-0-default/chat-process/demo",
//     {
//       prompt: params.prompt,
//       userId: window.location.hash,
//       network: !!params.network
//     },
//     {
//       signal: params.signal,
//       onDownloadProgress: params.onDownloadProgress
//     }
//   )
// }
export function fetchChatAPIProcess(params) {
  return fetch('/api/dds-server-wd/ws', {
    signal: params.signal,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      "question": params.prompt, 
      "type": "simple", 
      "model_name": "qwen-long" 
    }),
  })
}



