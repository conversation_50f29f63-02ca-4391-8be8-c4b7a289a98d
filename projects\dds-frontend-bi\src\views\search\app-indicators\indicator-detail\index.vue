<template>
  <DT-View
    :inner-style="{
      padding: '20px'
    }"
  >
    <!-- 页面标题与基础信息 -->
    <div class="indicator-top">
      <div class="indicator-left">
        <div class="indicator-head">
          <div class="indicator-type">{{ indicatorData.zblx || "-" }}</div>
          <div class="indicator-title">{{ indicatorData.zbmc || "-" }}</div>
          <div class="indicator-tab">
            {{ indicatorData.bq }}
          </div>

          <!-- <el-button
            type="primary"
            icon="el-icon-star-off"
            style="margin-left: auto"
          >
            收藏
          </el-button> -->
        </div>
        <div class="indicator-desc">
          <span class="desc-label">描述：</span>
          <span class="desc-value">
            {{ indicatorData.ms || "暂无内容" }}
          </span>
        </div>
        <!-- 核心数据展示 -->
        <div class="core-data" v-if="lxbm != 'yz'">
          <div class="main-data">
            {{ indicatorData.indValueShow || "-" }}

            <el-tooltip
              v-if="indicatorData.minThreshold && indicatorData.maxThreshold"
              class="item"
              effect="dark"
              :content="`阈值: ${indicatorData.minThreshold}~${indicatorData.maxThreshold}`"
              placement="top"
            >
              <div
                class="tag"
                :class="{
                  success: indicatorData.indStatus === 'normal',
                  warning: indicatorData.indStatus === 'error'
                }"
              >
                {{ indicatorData.indStatus === "normal" ? "正常" : "" }}
                {{ indicatorData.indStatus === "error" ? "异常" : "" }}
              </div>
            </el-tooltip>
          </div>
          <div class="compare-data">
            <div class="rate">
              <div class="text">环比：</div>
              <div
                class="icon"
                :class="[indicatorData.hbReslut > 0 ? 'up' : 'down']"
              ></div>
              <div
                class="count"
                :class="[indicatorData.hbReslut > 0 ? 'up' : 'down']"
              >
                {{ indicatorData.hbReslut || "-" }}%
              </div>
              <el-tooltip
                class="item"
                effect="dark"
                content="Top Center 提示文字"
                placement="top"
              >
                <div slot="content">
                  计算周期：
                  {{ getLabel(jszqList, indicatorData.calculateCycle) || "-" }}
                  <br />
                  上期计算时间： {{ indicatorData.hbLastCalculateTime || "-" }}
                  <br />
                  上期值：
                  {{ indicatorData.hbLastValue || "-" }}
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <div class="rate">
              <div class="text">同比：</div>
              <div
                class="icon"
                :class="[indicatorData.tbReslut > 2 ? 'up' : 'down']"
              ></div>
              <div
                class="count"
                :class="[indicatorData.tbReslut > 2 ? 'up' : 'down']"
              >
                {{ indicatorData.tbReslut || "-" }}%
              </div>
              <el-tooltip
                class="item"
                effect="dark"
                content="Top Center 提示文字"
                placement="top"
              >
                <div slot="content">
                  去年同期计算时间：{{
                    indicatorData.tbLastCalculateTime || "-"
                  }}
                  <br />
                  去年同期值：
                  {{ indicatorData.tbLastValue || "-" }}
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
          </div>
          <div class="core-right">
            <div class="data-time">
              <div class="label">数据计算时间：</div>
              {{ indicatorData.calculateTime || "-" }}
            </div>
            <div class="associated-indicator">
              <div class="label">关联指标：</div>
              <div class="text">
                <!-- 处理 indicatorData.glzb 为 null 或 undefined 的情况，默认返回空数组 -->
                <span
                  v-for="(zb, i) in indicatorData.linkIndicators"
                  :key="zb.currentName"
                >
                  <span class="t" @click="handleClick(zb)">
                    {{ zb.currentName }}
                  </span>
                  <!-- 修正索引判断条件，使用分割后的数组长度 -->
                  <span class="symbol">
                    {{
                      i === indicatorData.linkIndicators.length - 1 ? "" : "、"
                    }}
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧信息栏 -->
      <div class="indicator-right">
        <div class="info-item">
          <span>近30日浏览次数：</span>
          <span>{{ indicatorData.visitTimes || "-" }}次</span>
        </div>
        <div class="info-item">
          <span>创建时间：</span>
          <span>{{ indicatorData.createTime || "-" }}</span>
        </div>
        <div class="info-item">
          <span>所属指标域：</span>
          <span>{{ indicatorData.indicatorDomain || "未分组" }}</span>
        </div>
        <div class="info-item">
          <span>归属部门：</span>
          <span>{{ indicatorData.department || "暂无" }}</span>
        </div>
        <div class="info-item">
          <span>数据来源：</span>
          <span>
            {{
              indicatorData.dataSource &&
                indicatorData.dataSource
                  .map((item) => item.dataSourceName)
                  .join("、")
            }}
          </span>
        </div>
      </div>
      <div class="btns">
        <!-- <el-button type="primary" @click="addData">常模</el-button> -->
        <!-- <div class="rule-btn">预警规则</div> -->
      </div>
    </div>

    <el-tabs v-model="activeName" v-if="indicatorData.zbmc">
      <template v-if="lxbm != 'yz'">
        <el-tab-pane label="指标下钻" name="1">
          <IndicatorDown v-if="activeName == 1" />
        </el-tab-pane>
        <el-tab-pane label="变化趋势" name="2">
          <IndicatorTrend v-if="activeName == 2" />
        </el-tab-pane>
        <el-tab-pane label="指标血缘" name="3">
          <IndicatorBloon v-if="activeName == 3" />
        </el-tab-pane>
        <el-tab-pane label="内部归因" name="4">
          <IndicatorAttribution v-if="activeName == 4" />
        </el-tab-pane>
      </template>
      <template v-else>
        <el-tab-pane label="源数据" name="5">
          <IndicatorSourceDataAtom v-if="activeName == 5" />
        </el-tab-pane>
        <el-tab-pane label="指标变更记录" name="6">
          <IndicatorChangeRecord v-if="activeName == 6" />
        </el-tab-pane>
      </template>
      <template v-if="lxbm != 'yz'">
        <el-tab-pane label="源数据" name="5">
          <IndicatorSourceData v-if="activeName == 5" />
        </el-tab-pane>
        <el-tab-pane label="指标变更记录" name="6">
          <IndicatorChangeRecord v-if="activeName == 6" />
        </el-tab-pane>
        <el-tab-pane label="关联分析" name="7">
          <IndicatorRelation v-if="activeName == 7" />
        </el-tab-pane>
      </template>
    </el-tabs>
  </DT-View>
</template>

<script>
// 指标下钻
import IndicatorDown from "./IndicatorDown.vue"
// 变化趋势
import IndicatorTrend from "./IndicatorTrend.vue"
// 指标血缘
import IndicatorBloon from "./IndicatorBloon.vue"
// 指标归因
import IndicatorAttribution from "./IndicatorAttribution.vue"
// 源数据
import IndicatorSourceData from "./IndicatorSourceData.vue"

// 原子指标源数据
import IndicatorSourceDataAtom from "./IndicatorSourceDataAtom.vue"

// 变更记录
import IndicatorChangeRecord from "./IndicatorChangeRecord.vue"
// 关联分析
import IndicatorRelation from "./IndicatorRelation.vue"

import Request from "@/service"
import options from "../../../indicator-anagement/mixins/options"

export default {
  components: {
    IndicatorDown,
    IndicatorTrend,
    IndicatorBloon,
    IndicatorAttribution,
    IndicatorSourceData,
    IndicatorSourceDataAtom,
    IndicatorChangeRecord,
    IndicatorRelation
  },
  mixins: [options],
  data() {
    return {
      activeName: "1",
      lxbm: "",
      indCode: "",
      indicatorData: {
        zblx: "", // 指标类型
        zbmc: "", // 指标名称
        dataSource: []
      },
      viewGroup: [{ id: "0", name: "不限", children: [] }] // 数据域分组
    }
  },
  provide() {
    return {
      parent: this
    }
  },
  computed: {},
  created() {
    this.lxbm = this.$route.query.lxbm || ""
    if (this.lxbm === "yz") {
      this.activeName = "5"
    }
    this.indCode = this.$route.query.indCode || ""
    this.getBaseInfo()
  },
  mounted() {
    this.getAllViewGroup()
  },
  watch: {},
  methods: {
    async getBaseInfo() {
      const { data } = await Request.api.paramGet(
        "/zbgl/getIndicatorBaseDetail",
        {
          lxbm: this.lxbm,
          indCode: this.indCode
        }
      )
      if (data) {
        this.indicatorData = data
      } else {
        this.indicatorData = {}
      }
      console.log(data, ">>>>>>>>>>>>>>>>>>")
    },
    // 获取所有数据域分组
    async getAllViewGroup() {
      const { data } = await this.$httpBi.indicatorAnagement.getAllViewGroup()
      this.viewGroup[0].children = data
    },
    handleClick(row) {
      const routeUrl = this.$router.resolve({
        path: `/ddsBi/appDetail`,
        query: {
          indCode: row.currentCode,
          lxbm: row.currentType
        }
      })
      window.open(routeUrl.href, "_blank")
    },
    // 通用转换函数
    getLabel(options, value, labelKey = "label", valueKey = "value") {
      if (value === "" || value === null) {
        return "-"
      }
      const item = options.find(opt => opt[valueKey] === value)
      if (item) {
        return item[labelKey]
      }
      return value
    }
  }
}
</script>

<style scoped lang="scss">
.indicator-top {
  position: relative;
  display: flex;
  margin-bottom: 20px;
  .indicator-left {
    flex: 1;
    padding-right: 20px;
    border-right: 1px solid #e5e6eb;
    .indicator-head {
      display: flex;
      align-items: center;
      .indicator-type {
        padding: 0 8px;
        height: 24px;
        background: rgba(21, 99, 255, 0.08);
        border-radius: 4px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #1563ff;
        line-height: 24px;
        text-align: left;
        font-style: normal;
      }
      .indicator-title {
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 20px;
        color: #222222;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        margin-left: 8px;
        margin-right: 16px;
      }
      .indicator-tab {
        height: 12px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 12px;
        text-align: left;
        font-style: normal;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .indicator-desc {
      display: flex;
      margin-top: 8px;
      .desc-label {
        white-space: nowrap;
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 12px;
        color: #222222;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
      .desc-value {
        height: 40px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
    }
    .core-data {
      width: 100%;
      min-height: 80px;
      background: #f5f7fa;
      border-radius: 8px;
      display: flex;
      align-items: center;
      padding: 16px 24px;
      box-sizing: border-box;
      .main-data {
        display: flex;
        align-items: baseline;
        height: 32px;
        font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
        font-weight: 500;
        font-size: 32px;
        color: #222222;
        line-height: 32px;
        text-align: left;
        font-style: normal;
        white-space: nowrap;
        .tag {
          padding: 0 8px;
          height: 24px;
          border-radius: 4px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          line-height: 24px;
          text-align: center;
          font-style: normal;
          margin-left: 12px;
          &.success {
            background: rgba(0, 204, 136, 0.08);
            color: #00cc88;
          }
          &.warning {
            background: #ff5256;
            background: rgba(255, 82, 86, 0.08);
          }
        }
      }
      .compare-data {
        margin-left: 24px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        height: 100%;
        .rate {
          display: flex;
          align-items: center;
          .text {
            white-space: nowrap;
            height: 12px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 12px;
            text-align: left;
            font-style: normal;
          }
          .icon {
            width: 16px;
            height: 16px;
            &.up {
              background: url("~@/assets/images/up.png") no-repeat center;
              background-size: cover;
            }
            &.down {
              background: url("~@/assets/images/down.png") no-repeat center;
              background-size: cover;
            }
          }
          .count {
            height: 16px;
            font-family: AlibabaSans102Ver2;
            font-size: 16px;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            margin-right: 8px;
            white-space: nowrap;

            &.up {
              color: #00cc88;
            }
            &.down {
              color: #ff5256;
            }
          }
        }
      }
      .core-right {
        margin-left: 24px;
        border-left: 1px solid #e5e6eb;
        padding-left: 24px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .data-time {
          height: 12px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 12px;
          text-align: left;
          font-style: normal;
          margin-bottom: 16px;
          display: flex;
          .label {
            min-width: 85px;
            text-align: right;
            text-wrap: nowrap;
          }
        }
        .associated-indicator {
          display: flex;
          .label {
            min-width: 85px;
            height: 12px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 12px;
            text-align: right;
            font-style: normal;
            text-wrap: nowrap;
          }
          .text {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #1563ff;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            .t {
              cursor: pointer;
              &:hover {
                text-decoration: underline;
              }
            }
            .symbol {
              text-decoration: none;
              color: #999999;
            }
          }
        }
      }
    }
  }
  .indicator-right {
    padding-left: 20px;

    flex: 0 0 307px;
    .info-item {
      margin-bottom: 12px;
      display: flex;
      span:nth-child(1) {
        width: 108px;
        height: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 20px;
        text-align: right;
        font-style: normal;
      }
      span:nth-child(2) {
        flex: 1;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #222222;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
    }
  }
  .btns {
    position: absolute;
    right: 0px;
    bottom: -52px;
    z-index: 999;
    .rule-btn {
      width: 64px;
      height: 24px;
      border-radius: 4px;
      border: 1px solid #1563ff;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #1563ff;
      line-height: 24px;
      text-align: center;
      font-style: normal;
      cursor: pointer;
    }
  }
}
</style>
