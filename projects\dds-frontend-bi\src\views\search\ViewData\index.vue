<template>
  <div>
    <template v-if="sourceList.length">
      <p class="result">{{ sourceList.length }} 条相关结果</p>
      <div class="list">
        <div
          class="list-wrap"
          v-for="(item, index) in sourceList.slice(
            (currentPage - 1) * pagesize,
            currentPage * pagesize
          )"
          :key="index"
        >
          <div class="no-dimensions" v-if="item.isSingle">
            <div class="title-wrap">
              <div
                class="title"
                v-html="brightenKeyword(item.name, searchContent)"
              ></div>
              <span class="more" @click="handleDetail(item)">更多></span>
            </div>

            <div class="number">
              {{ item.zval | toThousands }}
              <span class="unit">{{ item.jldw }}</span>
            </div>
          </div>
          <div v-else class="list-item-custom" @click="handleDetail(item)">
            <div
              class="title"
              v-html="brightenKeyword(item.name, searchContent)"
            ></div>
            <div class="type">所属指标域：{{ item.sysjy }}</div>
            <div class="related">
              关联指标：{{
                item.glzb && item.glzb.map(item => item.zbmc || "").join("/")
              }}
            </div>
          </div>
        </div>
      </div>
      <div class="page">
        <el-pagination
          v-if="sourceList.length > 10"
          small
          layout="prev, pager, next"
          :current-page="currentPage"
          :total="sourceList.length"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </template>
    <Empty v-else />
  </div>
</template>

<script>
import { brightenKeyword } from "@/utils"
import Empty from "../Empty"
export default {
  components: { Empty },
  props: {
    sourceList: {
      type: Array,
      default: () => []
    },
    searchContent: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      listData: [],
      currentPage: 1,
      pagesize: 10
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    brightenKeyword,
    // 查看详情
    handleDetail({ lxbm, id, code, name, enable }) {
      if (!Number(enable)) {
        return this.$message({
          message: "数据正在计算中，请耐心等待2-3分钟",
          type: "warning"
        })
      }

      // 另标签打开
      const routerUrl = this.$router.resolve({
        path: name === "生师比" ? "/ddsBi/appDetail" : "/ddsBi/appDetail1",
        query: {
          indCode: code,
          id,
          lxbm
        }
      })
      window.open(routerUrl.href, "_blank")
    },
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style scoped lang="scss">
.result {
  height: 22px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #4e5969;
  line-height: 22px;
  margin-bottom: 16px;
}
.list {
  margin-bottom: 27px;
  .list-wrap {
    .list-item-custom {
      margin-bottom: 19px;
      padding-left: 16px;
      box-sizing: border-box;
      cursor: pointer;
      .title {
        font-size: 20px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        line-height: 28px;
        color: #323233;
        cursor: pointer;
        &:hover {
          color: #1463ff;
          text-decoration: underline;
        }
      }

      .type {
        font-size: 14px;

        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #4e5969;
        margin-top: 10px;
      }
      .related {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #86909c;
        line-height: 22px;
        margin-top: 8px;
      }
    }
    .no-dimensions {
      width: 560px;
      min-height: 96px;
      background: #ebf1ff;
      border-radius: 8px;
      padding: 22px 16px 20px 16px;
      margin-bottom: 24px;
      .title-wrap {
        display: flex;
        align-items: center;
        .title {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #1d2129;
          font-size: 18px;
          line-height: 18px;
          text-align: left;
          font-style: normal;
        }
        .more {
          min-width: max-content;
          font-size: 14px;
          color: #1463ff;
          margin-left: 24px;
          cursor: pointer;
        }
      }
      .number {
        margin-top: 14px;
        font-family: AlibabaSans102Ver2, AlibabaSans102Ver2;
        font-weight: 500;
        font-size: 28px;
        color: #1463ff;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        .unit {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 16px;
          color: #1d2129;
          margin-left: 6px;
        }
      }
    }
  }
}
.page {
  display: flex;
}
</style>
