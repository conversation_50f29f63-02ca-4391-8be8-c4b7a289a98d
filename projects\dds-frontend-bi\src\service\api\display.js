import service from "../base"
import config from "../config"

/**
 * 大屏
 */
export default {
  // 获取display列表
  getDisplayList(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/get",
      method: "get",
      params
    })
  },
  // 新增display
  addDisplay(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/create",
      method: "post",
      data
    })
  },
  // 删除display
  delDisplay(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/delete",
      method: "get",
      params
    })
  },
  // 更新display
  updDisplay(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/update",
      method: "post",
      data
    })
  },
  // 新建displaySlide
  addDisplaySlide(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/create/slide",
      method: "post",
      data
    })
  },
  // 获取display slides列表
  getDisplaySlideList(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/get/slides",
      method: "get",
      params
    })
  },
  // 更新displaySlide信息
  updDisplaySlides(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/update/slides",
      method: "post",
      data
    })
  },
  // 获取displaySlide下widgets关联信息列表
  getDisplaySlideWidgets(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/get/slide/widgets",
      method: "get",
      params
    })
  },
  // 删除slide
  delDisplaySlide(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/delete/slide",
      method: "get",
      params
    })
  },
  // 在displaySlide下新建widget关联
  addDisplaySlideWidget(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/add/slide/widgets",
      method: "post",
      data
    })
  },
  // 更新displaySlide信息
  updSlidesWidget(data) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/update/slide/widget",
      method: "post",
      data
    })
  },
  // 删除图层
  delSlidesWidget(params) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/delete/slide/widget",
      method: "get",
      params
    })
  },
  fileUploadPath(data) {
    return service({
      url: "/file-api/upms-service/upload",
      method: "post",
      data,
      headers: { "Content-Type": "multipart/form-data" }
    })
  },

  getLogo() {
    return service({
      url: `${config.VUE_MODULE_DISPLAY}manage/displayLogo?display=${window.location.pathname}?isFullPage=true`,
      method: "get"
    })
  },
  getDatasetData(parmas) {
    return service({
      url: config.VUE_MODULE_DDS_BI + "bi/display/getDatasetData",
      method: "post",
      data: parmas
    })
  },
  getWidgetData(key) {
    return service({
      url: "/dds-server-display/DisplayData/getWidgetData",
      method: "post",
      data: {
        key
      }
    })
  },
  paramGet(url, param) {
    let getParam = "?"
    for (var key in param) {
      getParam += key + "=" + param[key] + "&"
    }

    return service({
      url: config.VUE_MODULE_DDS_DISPLAY + url + getParam,
      method: "get"
    })
  },
  paramPost(url, param) {
    return service({
      url: config.VUE_MODULE_DDS_DISPLAY + url,
      method: "post",
      data: param
    })
  }
}
