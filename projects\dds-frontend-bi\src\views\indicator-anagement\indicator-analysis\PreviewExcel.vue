<template>
  <div v-loading="loading" class="preview-excel">
    <el-button type="primary" @click="downloadExcel">下载Excel</el-button>
    <div id="excel-preview"></div>
  </div>
</template>

<script>
import axios from 'axios'
import * as XLSX from 'xlsx'

export default {
  components: {},
  props: {},
  data () {
    return {
      loading: false
    }
  },
  computed: {},
  created () {
    const aiReportData = JSON.parse(this.$route.query.aiReportData)
    this.getTableData(aiReportData)
  },
  mounted () {},
  watch: {},
  methods: {
    async getTableData (aiReportData) {
      this.loading = true

      // 模拟API调用
      const { data } = await axios({
        method: 'post',
        url: '/api/dds-server-aiReport/report',
        data: aiReportData
      })
      console.log(data.file_name)
      this.excelUrl = 'http://***************' + data.file_name // 赋值Excel下载/预览地址
      fetch(this.excelUrl)
        .then(res => res.arrayBuffer())
        .then(data => {
          const workbook = XLSX.read(data)
          const sheetName = workbook.SheetNames[0]
          const html = XLSX.utils.sheet_to_html(workbook.Sheets[sheetName])
          document.getElementById('excel-preview').innerHTML = html
          this.loading = false
        })
    },
    downloadExcel () {
      if (this.excelUrl) {
        window.open(this.excelUrl)
      } else {
        this.$message.warning('暂无可下载的Excel文件')
      }
    }
  }
}
</script>

<style lang="scss">
.preview-excel {
  width: 100vw;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  background: #f5f7fa;
}

#excel-preview {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  background: #fff;
  table {
    width: 100%;
    border: 1px solid #e4e7ed;
  }
  th,
  td {
    border: 1px solid #e4e7ed;
    padding: 8px;
    text-align: center;
    font-size: 14px;
    color: #303133;
    min-width: 80px;
    max-width: 300px;
    word-break: break-all;
  }
  th {
    background: #f5f7fa;
    font-weight: bold;
  }
  tr:hover {
    background: #f0f9ff;
  }
}
</style>
