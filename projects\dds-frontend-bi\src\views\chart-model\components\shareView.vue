<template>
  <el-dialog
    title="图表模型分享"
    :visible.sync="dialogVisible"
    width="880px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="数据使用场景">
        <el-input v-model="form.dataScenarios"></el-input>
      </el-form-item>
      <el-form-item label="图表ID">
        {{ form.chartCode }}
      </el-form-item>
      <el-form-item label="名称">
        {{ form.chartName }}
      </el-form-item>
      <el-form-item label="数据预览">
        <div class="content" style="height: 350px; width: 100%">
          <AceEditor v-model="form.dataJSON" />
        </div>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="copyApi">生成并复制API</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Request from "@/service"
import AceEditor from "@/components/AceEditor"
import { copyText } from "@/utils/format"

export default {
  components: { AceEditor },
  props: {},
  data() {
    return {
      dialogVisible: false,
      form: {
        dataScenarios: ""
      }
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    async openDialog(row) {
      console.log(row)
      this.form = {
        ...row,
        dataScenarios: ""
      }
      const { data } = await Request.api.paramGet(
        "/indicator/chart/getChartSearchData",
        {
          chatCode: row.chartCode,
          currentPage: 1,
          pageSize: 20
        }
      )
      this.form.dataJSON = JSON.stringify(data, null, 2)
      console.log(data)

      this.dialogVisible = true
    },
    // 生成并复制API
    copyApi() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const { data } = await Request.api.paramPost(
            "/indicator/chart/createShare",
            {
              ...this.form
            }
          )
          console.log(data)
          if (data) {
            this.$message.success("复制成功")
            copyText({ text: data.shareApiUrl })
            this.dialogVisible = false
          }
          this.$emit("refresh")
        } else {
          if (!valid) {
            return false
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
</style>
